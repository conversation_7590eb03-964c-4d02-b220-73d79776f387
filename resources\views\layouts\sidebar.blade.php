<aside class="bg-gray-800 text-white w-64 min-h-screen flex-shrink-0 flex flex-col justify-between">
    <div>
        <!-- <PERSON><PERSON> and App Name Header -->
        <div class="flex items-center p-4 border-b border-gray-700">
            <div class="mr-3">
                <img src="{{ env('APP_LOGO_URL', 'https://via.placeholder.com/40x40/4F46E5/FFFFFF?text=' . substr(env('APP_NAME', 'App'), 0, 1)) }}"
                     alt="{{ env('APP_NAME', 'Application') }} Logo"
                     class="w-10 h-10 rounded-full object-cover">
            </div>
            <div class="flex-1 min-w-0">
                <h2 class="text-lg font-bold text-white truncate">{{ env('APP_NAME', 'Sales Management System') }}</h2>
                <p class="text-sm text-gray-300 truncate">Management Portal</p>
            </div>
        </div>

        <nav class="p-4">
            <div class="space-y-2 flex flex-col">
            @foreach($menuItems as $item)
                <a href="{{ route($item['route']) }}"
                    class="flex items-center p-2 rounded hover:bg-gray-700 space-x-3 no-underline">
                    <i class="fas fa-{{ $item['icon'] }} sidebar-icon"></i>
                    <span class="flex-1">{{ $item['label'] }}</span>
                </a>
            @endforeach
            </div>
        </nav>
    </div>

    <!-- Settings Link -->
    @hasrole('Organization Owner|Manager')
    <div class="space-y-1 mt-4 p-4">
        <x-sidebar-link
            href="{{ route('settings.index') }}"
            icon="cog"
            :active="request()->routeIs('settings.index')">
            App Settings
        </x-sidebar-link>

        <x-sidebar-link
            href="{{ route('settings.business') }}"
            icon="building-office"
            :active="request()->routeIs('settings.business')">
            Business Settings
        </x-sidebar-link>

        <x-sidebar-link
            href="{{ route('settings.receipt') }}"
            icon="receipt-tax"
            :active="request()->routeIs('settings.receipt')">
            Receipt Settings
        </x-sidebar-link>

        <x-sidebar-link
            href="{{ route('settings.notifications') }}"
            icon="bell"
            :active="request()->routeIs('settings.notifications')">
            Notification Settings
        </x-sidebar-link>

        <x-sidebar-link
            href="{{ route('settings.printer') }}"
            icon="printer"
            :active="request()->routeIs('settings.printer')">
            Printer Settings
        </x-sidebar-link>
    </div>
    @endhasrole
</aside>

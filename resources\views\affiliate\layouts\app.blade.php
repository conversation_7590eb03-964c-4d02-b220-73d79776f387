<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'Affiliate Dashboard') - {{ config('app.name', 'Sederly Solutions') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- International Telephone Input -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/css/intlTelInput.css">

    <!-- Custom CSS -->
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(180deg, #8e44ad 0%, #9b59b6 100%);
        }
        .sidebar .nav-link {
            color: #ecf0f1;
            padding: 0.75rem 1rem;
            border-radius: 0.375rem;
            margin: 0.125rem 0;
        }
        .sidebar .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: #fff;
        }
        .sidebar .nav-link.active {
            background-color: #e74c3c;
            color: #fff;
        }
        .border-left-primary {
            border-left: 0.25rem solid #8e44ad !important;
        }
        .border-left-success {
            border-left: 0.25rem solid #27ae60 !important;
        }
        .border-left-info {
            border-left: 0.25rem solid #3498db !important;
        }
        .border-left-warning {
            border-left: 0.25rem solid #f39c12 !important;
        }
        .border-left-danger {
            border-left: 0.25rem solid #e74c3c !important;
        }
        .text-gray-800 {
            color: #5a5c69 !important;
        }
        .text-gray-300 {
            color: #dddfeb !important;
        }
        .text-primary {
            color: #8e44ad !important;
        }
        .bg-primary {
            background-color: #8e44ad !important;
        }
        .btn-primary {
            background-color: #8e44ad;
            border-color: #8e44ad;
        }
        .btn-primary:hover {
            background-color: #7d3c98;
            border-color: #7d3c98;
        }
        .progress-bar {
            background-color: #8e44ad !important;
        }

        /* Email Verification Success Popup */
        .email-verification-popup {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: fadeIn 0.5s ease-out;
        }

        .popup-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            /* Clean dark overlay for better focus on popup */
        }

        .popup-content {
            position: relative;
            background: white;
            border-radius: 20px;
            padding: 0;
            max-width: 500px;
            width: 90%;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: slideInUp 0.6s ease-out;
            overflow: hidden;
        }

        .popup-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            text-align: center;
            padding: 2rem 1.5rem 1.5rem;
            position: relative;
        }

        .success-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            animation: bounceIn 0.8s ease-out 0.3s both;
        }

        .popup-header h3 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
            animation: fadeInUp 0.6s ease-out 0.5s both;
        }

        .popup-body {
            padding: 2rem 1.5rem;
            text-align: center;
        }

        .popup-body p {
            font-size: 1.1rem;
            color: #495057;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .celebration-icons {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .celebration-icons i {
            font-size: 1.5rem;
            color: #ffc107;
            animation: sparkle 1.5s ease-in-out infinite;
        }

        .celebration-icons i:nth-child(2) {
            animation-delay: 0.3s;
        }

        .celebration-icons i:nth-child(3) {
            animation-delay: 0.6s;
        }

        .popup-footer {
            padding: 0 1.5rem 2rem;
            text-align: center;
        }

        .popup-footer .btn {
            min-width: 200px;
            border-radius: 50px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        .popup-footer .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(40, 167, 69, 0.4);
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            50% {
                opacity: 1;
                transform: scale(1.1);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes sparkle {
            0%, 100% {
                transform: scale(1) rotate(0deg);
                opacity: 0.7;
            }
            50% {
                transform: scale(1.2) rotate(180deg);
                opacity: 1;
            }
        }

        /* Responsive */
        @media (max-width: 576px) {
            .popup-content {
                width: 95%;
                margin: 1rem;
            }

            .popup-header {
                padding: 1.5rem 1rem 1rem;
            }

            .success-icon {
                font-size: 3rem;
            }

            .popup-header h3 {
                font-size: 1.3rem;
            }

            .popup-body {
                padding: 1.5rem 1rem;
            }

            .popup-footer {
                padding: 0 1rem 1.5rem;
            }

            .popup-footer .btn {
                min-width: 150px;
                font-size: 0.9rem;
            }
        }
    </style>

    @stack('styles')
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <!-- Logo and App Name -->
                    <div class="d-flex align-items-center mb-4 px-2">
                        <div class="me-3">
                            <img src="{{ env('APP_LOGO_URL', 'https://via.placeholder.com/40x40/4F46E5/FFFFFF?text=' . substr(env('APP_NAME', 'App'), 0, 1)) }}"
                                 alt="{{ env('APP_NAME', 'Application') }} Logo"
                                 class="rounded-circle"
                                 style="width: 40px; height: 40px; object-fit: cover;">
                        </div>
                        <div class="flex-grow-1">
                            <h5 class="text-white mb-1 fw-bold">{{ env('APP_NAME', 'Sales Management System') }}</h5>
                            <small class="text-light opacity-75">Affiliate Portal</small>
                        </div>
                    </div>

                    <!-- Navigation -->
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('affiliate.dashboard') ? 'active' : '' }}"
                               href="{{ route('affiliate.dashboard') }}">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Dashboard
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('affiliate.referrals') ? 'active' : '' }}"
                               href="{{ route('affiliate.referrals') }}">
                                <i class="fas fa-users me-2"></i>
                                My Referrals
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('affiliate.referral-tools') ? 'active' : '' }}"
                               href="{{ route('affiliate.referral-tools') }}">
                                <i class="fas fa-link me-2"></i>
                                Referral Tools
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('affiliate.earnings') ? 'active' : '' }}"
                               href="{{ route('affiliate.earnings') }}">
                                <i class="fas fa-chart-line me-2"></i>
                                Earnings
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('affiliate.withdrawals*') ? 'active' : '' }}"
                               href="{{ route('affiliate.withdrawals') }}">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                Withdrawals
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('affiliate.profile') ? 'active' : '' }}"
                               href="{{ route('affiliate.profile') }}">
                                <i class="fas fa-user-cog me-2"></i>
                                Profile
                            </a>
                        </li>

                        <hr class="my-3" style="border-color: rgba(255,255,255,0.2);">

                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('affiliate.program') }}">
                                <i class="fas fa-info-circle me-2"></i>
                                Program Info
                            </a>
                        </li>

                        <li class="nav-item">
                            <form method="POST" action="{{ route('affiliate.logout') }}" class="d-inline">
                                @csrf
                                <button type="submit" class="nav-link border-0 bg-transparent text-start w-100">
                                    <i class="fas fa-sign-out-alt me-2"></i>
                                    Logout
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- Top Navigation -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <div class="d-flex align-items-center">
                        <button class="btn btn-outline-secondary d-md-none me-2" type="button" data-bs-toggle="collapse" data-bs-target=".sidebar">
                            <i class="fas fa-bars"></i>
                        </button>
                        <h1 class="h2 mb-0">@yield('page-title', 'Dashboard')</h1>
                    </div>

                    <div class="d-flex align-items-center">
                        <!-- Currency Override Indicator -->
                        @php
                            $overrideEnabled = \App\Models\CurrencySetting::get('system_currency_override_enabled', false);
                            $overrideCurrency = \App\Models\CurrencySetting::get('system_currency_override', 'USD');
                        @endphp

                        @if($overrideEnabled)
                            <div class="me-3">
                                <span class="badge bg-warning text-dark">
                                    <i class="fas fa-globe-americas me-1"></i>
                                    System Override: {{ $overrideCurrency }}
                                </span>
                            </div>
                        @endif

                        <!-- Current Currency Display -->
                        <div class="me-3 text-center">
                            <div class="small text-muted">Currency</div>
                            <span class="badge bg-{{ user_currency() === 'NGN' ? 'success' : 'primary' }}">
                                {{ user_currency_symbol() }} {{ user_currency() }}
                            </span>
                        </div>

                        <!-- User Info -->
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                {{ Auth::guard('affiliate')->user()->name }}
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ route('affiliate.profile') }}">
                                    <i class="fas fa-user-cog me-2"></i>Profile
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form method="POST" action="{{ route('affiliate.logout') }}" class="d-inline">
                                        @csrf
                                        <button type="submit" class="dropdown-item">
                                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Flash Messages -->
                @if(session('success'))
                    @if(str_contains(session('success'), 'Email verified successfully') || str_contains(session('success'), 'verified successfully'))
                        <!-- Email Verification Success Popup -->
                        <div id="emailVerificationPopup" class="email-verification-popup" style="display: none;">
                            <div class="popup-content">
                                <div class="popup-header">
                                    <div class="success-icon">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <h3>🎉 Email Verified Successfully!</h3>
                                </div>
                                <div class="popup-body">
                                    <p class="mb-3">{{ session('success') }}</p>
                                    <div class="verification-details">
                                        <div class="row text-center mb-3">
                                            <div class="col-4">
                                                <i class="fas fa-check-circle text-success mb-2" style="font-size: 1.5rem;"></i>
                                                <small class="d-block text-muted">Email Verified</small>
                                            </div>
                                            <div class="col-4">
                                                <i class="fas fa-user-check text-success mb-2" style="font-size: 1.5rem;"></i>
                                                <small class="d-block text-muted">Account Active</small>
                                            </div>
                                            <div class="col-4">
                                                <i class="fas fa-rocket text-success mb-2" style="font-size: 1.5rem;"></i>
                                                <small class="d-block text-muted">Ready to Earn</small>
                                            </div>
                                        </div>
                                    </div>
                                    <p class="text-muted small mb-3">You can now access all affiliate features and start earning commissions!</p>
                                    <div class="celebration-icons">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                    </div>
                                </div>
                                <div class="popup-footer">
                                    <button type="button" class="btn btn-success btn-lg" onclick="closeEmailVerificationPopup()">
                                        <i class="fas fa-rocket me-2"></i>
                                        Get Started!
                                    </button>
                                </div>
                            </div>
                            <div class="popup-overlay" onclick="closeEmailVerificationPopup()"></div>
                        </div>
                    @else
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif
                @endif

                @if(session('error'))
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        {{ session('error') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @if(session('warning'))
                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        {{ session('warning') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @if(session('info'))
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        {{ session('info') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @if($errors->any())
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <strong>Please fix the following errors:</strong>
                        <ul class="mb-0 mt-2">
                            @foreach($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                <!-- Page Content -->
                @yield('content')
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- International Telephone Input -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/intlTelInput.min.js"></script>

    <!-- Custom JS -->
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);

        // Email Verification Popup Functions
        function closeEmailVerificationPopup() {
            const popup = document.getElementById('emailVerificationPopup');
            if (popup) {
                popup.style.animation = 'fadeOut 0.3s ease-out';
                setTimeout(function() {
                    popup.remove();
                }, 300);
            }
        }

        // Create confetti effect
        function createConfetti() {
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3', '#54a0ff'];
            const confettiCount = 50;

            for (let i = 0; i < confettiCount; i++) {
                const confetti = document.createElement('div');
                confetti.style.position = 'fixed';
                confetti.style.width = '10px';
                confetti.style.height = '10px';
                confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
                confetti.style.left = Math.random() * 100 + 'vw';
                confetti.style.top = '-10px';
                confetti.style.zIndex = '10000';
                confetti.style.borderRadius = '50%';
                confetti.style.pointerEvents = 'none';
                confetti.style.animation = `confettiFall ${Math.random() * 3 + 2}s linear forwards`;

                document.body.appendChild(confetti);

                setTimeout(() => {
                    confetti.remove();
                }, 5000);
            }
        }

        // Show loading message and then popup
        document.addEventListener('DOMContentLoaded', function() {
            const popup = document.getElementById('emailVerificationPopup');
            if (popup) {
                // Show brief loading message
                showVerificationLoadingMessage();

                // Show popup after brief delay
                setTimeout(function() {
                    hideVerificationLoadingMessage();
                    popup.style.display = 'flex';

                    // Add confetti effect
                    setTimeout(createConfetti, 500);
                }, 1000);

                // Auto-close after 15 seconds
                setTimeout(function() {
                    closeEmailVerificationPopup();
                }, 16000);
            }
        });

        // Show loading message
        function showVerificationLoadingMessage() {
            const loadingHTML = `
                <div id="verificationLoading" style="
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: white;
                    padding: 2rem;
                    border-radius: 10px;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                    text-align: center;
                    z-index: 9998;
                ">
                    <div class="spinner-border text-success mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mb-0 text-muted">Verifying your email...</p>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', loadingHTML);
        }

        // Hide loading message
        function hideVerificationLoadingMessage() {
            const loading = document.getElementById('verificationLoading');
            if (loading) {
                loading.remove();
            }
        }

        // Add fadeOut and confetti animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeOut {
                from { opacity: 1; }
                to { opacity: 0; }
            }

            @keyframes confettiFall {
                0% {
                    transform: translateY(-10px) rotate(0deg);
                    opacity: 1;
                }
                100% {
                    transform: translateY(100vh) rotate(720deg);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>

    @stack('scripts')
</body>
</html>
